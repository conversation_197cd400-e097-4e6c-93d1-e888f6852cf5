# BC Monitoring Service - Adhoc Tests

This directory contains adhoc tests for the BC Monitoring Service based on the test matrix defined in `BC_Monitoring_Service_Test_Matrix.md`.

## Overview

The adhoc tests use TestContainers to set up local instances of:
- **LocalStack S3 service** - for ABI file storage and retrieval
- **DynamoDB service** - for event and block height persistence  
- **Mock Besu blockchain** - simulated blockchain interactions

## Test Infrastructure

### Core Components

1. **BcMonitoringAdhocHelper** - Main test utility class that:
   - Manages TestContainers lifecycle
   - Sets up LocalStack services (S3, DynamoDB)
   - Creates test data and configurations
   - Provides utility methods for test setup

2. **MockBesuService** - Mock blockchain service that:
   - Simulates Web3j blockchain interactions
   - Allows controlled block mining and event emission
   - Handles WebSocket connection simulation
   - Supports error scenario testing

3. **Docker Compose Configuration** - `docker-compose_adhoc.yml`:
   - LocalStack container with S3 and DynamoDB services
   - Health checks and proper service dependencies

## Test Categories

### 1. Service Startup Tests (`service/`)
- **ServiceStartupAdhocSpec** - Tests successful service initialization
- Covers test cases from section 1 of the test matrix
- Verifies all dependencies are available and accessible

### 2. ABI Management Tests (`abi/`)
- **AbiDownloadAdhocSpec** - Tests ABI file download and parsing
- Covers test cases from section 2 of the test matrix
- Tests multiple zone processing, format handling, file filtering

### 3. Event Monitoring Tests (`monitoring/`)
- **EventMonitoringAdhocSpec** - Tests blockchain event detection
- Covers test cases from section 3 of the test matrix
- Tests event processing, block height updates, error handling

## Running the Tests

### Prerequisites
- Docker and Docker Compose installed
- Java 21
- Gradle

### Execute Adhoc Tests
```bash
# Run all adhoc tests
./gradlew testAdhoc

# Run specific test category
./gradlew testAdhoc --tests "adhoc.service.*"
./gradlew testAdhoc --tests "adhoc.abi.*"
./gradlew testAdhoc --tests "adhoc.monitoring.*"

# Run with debug logging
./gradlew testAdhoc -Dlogging.level.com.decurret_dcp.dcjpy.bcmonitoring=DEBUG
```

### Test Configuration
- Test properties: `src/test/resources/adhoc/application-adhoc.properties`
- Sample ABI files: `src/test/resources/adhoc/abi/`
- Docker configuration: `docker-compose_adhoc.yml`

## Test Matrix Coverage

### Implemented Test Cases (3 of 37 total)

#### 1.1.1 Successful Service Startup ✅
- **File**: `service/ServiceStartupAdhocSpec.groovy`
- **Coverage**: Service initialization with all dependencies
- **Verifies**: S3 access, DynamoDB connectivity, configuration loading

#### 2.1.1 Successful ABI Download and Parsing ✅  
- **File**: `abi/AbiDownloadAdhocSpec.groovy`
- **Coverage**: ABI file processing from S3
- **Verifies**: Multi-zone processing, format handling, file filtering

#### 3.1.1 New Block Event Detection ✅
- **File**: `monitoring/EventMonitoringAdhocSpec.groovy` 
- **Coverage**: Blockchain event monitoring and processing
- **Verifies**: Event detection, DynamoDB storage, block height updates

### Remaining Test Cases (34 pending)

The test infrastructure is designed to be easily extensible. Additional test cases can be added by:

1. Creating new test specification files in appropriate directories
2. Using the existing `BcMonitoringAdhocHelper` and `MockBesuService` utilities
3. Following the established patterns for TestContainers and Spring Boot testing

### Future Test Cases to Implement

**High Priority:**
- 1.3.1 Service Startup Failure - Invalid Environment Variables
- 2.3.1 S3 Bucket Access Denied  
- 3.3.1 WebSocket Connection Failure
- 4.3.1 DynamoDB Write Failure

**Medium Priority:**
- 1.2.1 Service Startup with Empty ABI Bucket
- 2.2.1 Non-JSON Files in S3 Bucket
- 3.2.1 Block with No Matching Events
- 4.1.1 Event Storage to DynamoDB

## Architecture Notes

### TestContainers Integration
- Uses Docker Compose for service orchestration
- LocalStack provides AWS service simulation
- Containers are shared across test methods for performance

### Mock Strategy
- Real AWS SDK clients against LocalStack (S3, DynamoDB)
- Mock Web3j for blockchain interactions
- Controlled test data and scenarios

### Spring Boot Testing
- `@SpringBootTest` with test configuration
- `@DynamicPropertySource` for runtime configuration
- Test-specific beans and mocking where needed

## Troubleshooting

### Common Issues
1. **Docker not available**: Ensure Docker is running
2. **Port conflicts**: Check if ports 4566-4599 are available
3. **Container startup timeout**: Increase timeout in test configuration
4. **Test isolation**: Each test should clean up its data

### Debug Tips
- Enable debug logging: `-Dlogging.level.com.decurret_dcp.dcjpy.bcmonitoring=DEBUG`
- Check container logs: `docker-compose -f docker-compose_adhoc.yml logs`
- Verify LocalStack health: `curl http://localhost:4566/_localstack/health`

## Contributing

When adding new test cases:
1. Follow the existing naming conventions
2. Use the provided helper utilities
3. Document the test matrix coverage
4. Ensure proper cleanup and test isolation
5. Add appropriate assertions and error scenarios
