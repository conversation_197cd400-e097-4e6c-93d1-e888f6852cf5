package adhoc.abi

import adhoc.test.util.BcMonitoringAdhocHelper
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.application.abi.DownloadAbiService
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.springframework.test.context.TestPropertySource
import org.testcontainers.spock.Testcontainers
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import spock.lang.Shared
import spock.lang.Specification

/**
 * Adhoc test for ABI File Management
 * 
 * Test Case 2.1.1: Successful ABI Download and Parsing
 * - Description: Downloads and parses valid ABI files from S3
 * - Input: S3 bucket with valid JSON ABI files in correct directory structure (e.g., "3000/Contract.json")
 * - Expected Result: ABI files parsed successfully, contract addresses and events stored in memory
 */
@Testcontainers
@SpringBootTest(
        classes = BcmonitoringApplication,
        webEnvironment = SpringBootTest.WebEnvironment.NONE,
        properties = [
                "spring.main.web-application-type=none",
                "logging.level.com.decurret_dcp.dcjpy.bcmonitoring=DEBUG"
        ]
)
@TestPropertySource(locations = "classpath:adhoc/application-adhoc.properties")
class AbiDownloadAdhocSpec extends Specification {

    @Shared
    static BcMonitoringAdhocHelper adhocHelper = new BcMonitoringAdhocHelper()

    @Autowired
    DownloadAbiService downloadAbiService

    @Autowired
    AbiParser abiParser

    private static final ObjectMapper mapper = new ObjectMapper()

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        adhocHelper.initAdhoc(registry)
    }

    def cleanupSpec() {
        adhocHelper.stopContainer()
    }

    def "should download and parse valid ABI files from S3"() {
        given: "S3 bucket contains valid ABI files in correct directory structure"
        def s3Client = adhocHelper.createS3Client()
        
        // Create a more comprehensive ABI file
        def validAbi = [
            "contractName": "TestContract",
            "abi": [
                [
                    "type": "event",
                    "name": "Transfer",
                    "inputs": [
                        ["name": "from", "type": "address", "indexed": true],
                        ["name": "to", "type": "address", "indexed": true],
                        ["name": "value", "type": "uint256", "indexed": false]
                    ]
                ],
                [
                    "type": "event", 
                    "name": "Approval",
                    "inputs": [
                        ["name": "owner", "type": "address", "indexed": true],
                        ["name": "spender", "type": "address", "indexed": true],
                        ["name": "value", "type": "uint256", "indexed": false]
                    ]
                ]
            ],
            "address": "0x1234567890123456789012345678901234567890"
        ]
        
        def abiJson = mapper.writeValueAsString(validAbi)
        
        // Upload to multiple zones to test zone processing
        ["3000", "3001", "3002"].each { zone ->
            s3Client.putObject(
                    PutObjectRequest.builder()
                            .bucket("abijson-local-bucket")
                            .key("${zone}/TestContract.json")
                            .build(),
                    RequestBody.fromString(abiJson)
            )
        }

        when: "ABI download service is executed"
        downloadAbiService.execute()

        then: "No exceptions should be thrown"
        noExceptionThrown()

        and: "S3 objects should be accessible and processed"
        def response = s3Client.listObjectsV2 { builder ->
            builder.bucket("abijson-local-bucket")
        }
        response.contents().size() >= 3 // At least 3 files uploaded

        cleanup:
        s3Client.close()
    }

    def "should handle multiple zone ABI processing"() {
        given: "S3 bucket contains ABI files in multiple zone directories"
        def s3Client = adhocHelper.createS3Client()
        
        def zones = ["3000", "3001", "3002", "3003"]
        zones.each { zone ->
            def abiForZone = [
                "contractName": "Contract${zone}",
                "abi": [
                    [
                        "type": "event",
                        "name": "ZoneEvent${zone}",
                        "inputs": [
                            ["name": "zoneId", "type": "uint256", "indexed": false]
                        ]
                    ]
                ],
                "address": "0x${zone}567890123456789012345678901234567890"
            ]
            
            def abiJson = mapper.writeValueAsString(abiForZone)
            s3Client.putObject(
                    PutObjectRequest.builder()
                            .bucket("abijson-local-bucket")
                            .key("${zone}/Contract${zone}.json")
                            .build(),
                    RequestBody.fromString(abiJson)
            )
        }

        when: "ABI download service processes all zones"
        downloadAbiService.execute()

        then: "All zone ABI files should be processed successfully"
        noExceptionThrown()

        and: "All zone directories should contain files"
        zones.each { zone ->
            def zoneResponse = s3Client.listObjectsV2 { builder ->
                builder.bucket("abijson-local-bucket")
                       .prefix("${zone}/")
            }
            assert zoneResponse.contents().size() > 0
        }

        cleanup:
        s3Client.close()
    }

    def "should handle hardhat vs truffle ABI format correctly"() {
        given: "ABI files in both hardhat and truffle formats"
        def s3Client = adhocHelper.createS3Client()
        
        // Hardhat format (address at root level)
        def hardhatAbi = [
            "contractName": "HardhatContract",
            "abi": [
                [
                    "type": "event",
                    "name": "HardhatEvent",
                    "inputs": [
                        ["name": "value", "type": "uint256", "indexed": false]
                    ]
                ]
            ],
            "address": "0x1111111111111111111111111111111111111111"
        ]
        
        // Truffle format (address in networks)
        def truffleAbi = [
            "contractName": "TruffleContract",
            "abi": [
                [
                    "type": "event",
                    "name": "TruffleEvent", 
                    "inputs": [
                        ["name": "value", "type": "uint256", "indexed": false]
                    ]
                ]
            ],
            "networks": [
                "1": [
                    "address": "0x2222222222222222222222222222222222222222"
                ]
            ]
        ]
        
        s3Client.putObject(
                PutObjectRequest.builder()
                        .bucket("abijson-local-bucket")
                        .key("3000/HardhatContract.json")
                        .build(),
                RequestBody.fromString(mapper.writeValueAsString(hardhatAbi))
        )
        
        s3Client.putObject(
                PutObjectRequest.builder()
                        .bucket("abijson-local-bucket")
                        .key("3000/TruffleContract.json")
                        .build(),
                RequestBody.fromString(mapper.writeValueAsString(truffleAbi))
        )

        when: "ABI download service processes both formats"
        downloadAbiService.execute()

        then: "Both formats should be processed successfully"
        noExceptionThrown()

        and: "Files should be accessible"
        def response = s3Client.listObjectsV2 { builder ->
            builder.bucket("abijson-local-bucket")
                   .prefix("3000/")
        }
        response.contents().any { it.key().contains("HardhatContract.json") }
        response.contents().any { it.key().contains("TruffleContract.json") }

        cleanup:
        s3Client.close()
    }

    def "should skip non-JSON files in S3 bucket"() {
        given: "S3 bucket contains both JSON and non-JSON files"
        def s3Client = adhocHelper.createS3Client()
        
        // Upload non-JSON files
        s3Client.putObject(
                PutObjectRequest.builder()
                        .bucket("abijson-local-bucket")
                        .key("3000/README.txt")
                        .build(),
                RequestBody.fromString("This is a readme file")
        )
        
        s3Client.putObject(
                PutObjectRequest.builder()
                        .bucket("abijson-local-bucket")
                        .key("3000/config.yaml")
                        .build(),
                RequestBody.fromString("config: value")
        )
        
        // Upload valid JSON file
        def validAbi = [
            "contractName": "ValidContract",
            "abi": [
                [
                    "type": "event",
                    "name": "ValidEvent",
                    "inputs": []
                ]
            ],
            "address": "0x3333333333333333333333333333333333333333"
        ]
        
        s3Client.putObject(
                PutObjectRequest.builder()
                        .bucket("abijson-local-bucket")
                        .key("3000/ValidContract.json")
                        .build(),
                RequestBody.fromString(mapper.writeValueAsString(validAbi))
        )

        when: "ABI download service is executed"
        downloadAbiService.execute()

        then: "Non-JSON files should be skipped, JSON files processed"
        noExceptionThrown()

        and: "All files should be present in S3"
        def response = s3Client.listObjectsV2 { builder ->
            builder.bucket("abijson-local-bucket")
                   .prefix("3000/")
        }
        response.contents().any { it.key().contains("README.txt") }
        response.contents().any { it.key().contains("config.yaml") }
        response.contents().any { it.key().contains("ValidContract.json") }

        cleanup:
        s3Client.close()
    }

    def "should handle nested directory structure correctly"() {
        given: "S3 bucket with files in nested directories"
        def s3Client = adhocHelper.createS3Client()
        
        // Direct child (should be processed)
        def directAbi = [
            "contractName": "DirectContract",
            "abi": [["type": "event", "name": "DirectEvent", "inputs": []]],
            "address": "0x4444444444444444444444444444444444444444"
        ]
        
        s3Client.putObject(
                PutObjectRequest.builder()
                        .bucket("abijson-local-bucket")
                        .key("3000/DirectContract.json")
                        .build(),
                RequestBody.fromString(mapper.writeValueAsString(directAbi))
        )
        
        // Nested file (should be skipped)
        def nestedAbi = [
            "contractName": "NestedContract",
            "abi": [["type": "event", "name": "NestedEvent", "inputs": []]],
            "address": "0x5555555555555555555555555555555555555555"
        ]
        
        s3Client.putObject(
                PutObjectRequest.builder()
                        .bucket("abijson-local-bucket")
                        .key("3000/nested/NestedContract.json")
                        .build(),
                RequestBody.fromString(mapper.writeValueAsString(nestedAbi))
        )

        when: "ABI download service is executed"
        downloadAbiService.execute()

        then: "Only direct child files should be processed, nested files skipped"
        noExceptionThrown()

        and: "Both files should exist in S3"
        def response = s3Client.listObjectsV2 { builder ->
            builder.bucket("abijson-local-bucket")
                   .prefix("3000/")
        }
        response.contents().any { it.key() == "3000/DirectContract.json" }
        response.contents().any { it.key() == "3000/nested/NestedContract.json" }

        cleanup:
        s3Client.close()
    }
}
