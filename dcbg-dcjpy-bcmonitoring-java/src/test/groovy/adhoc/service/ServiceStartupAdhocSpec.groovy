package adhoc.service

import adhoc.test.util.BcMonitoringAdhocHelper
import adhoc.test.util.MockBesuService
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.application.abi.DownloadAbiService
import com.decurret_dcp.dcjpy.bcmonitoring.application.event.MonitorEventService
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.springframework.test.context.TestPropertySource
import org.testcontainers.spock.Testcontainers
import org.web3j.protocol.Web3j
import spock.lang.Shared
import spock.lang.Specification
import spock.util.concurrent.PollingConditions

/**
 * Adhoc test for BC Monitoring Service Startup
 * 
 * Test Case 1.1.1: Successful Service Startup
 * - Description: Service starts successfully with all dependencies available
 * - Input: Valid environment variables, accessible S3 bucket with valid ABI files, 
 *          accessible DynamoDB, accessible Ethereum WebSocket endpoint
 * - Expected Result: Service logs "Starting bc monitoring", begins monitoring blockchain events
 */
@Testcontainers
@SpringBootTest(
        classes = [BcmonitoringApplication, ServiceStartupAdhocSpec.TestConfig],
        webEnvironment = SpringBootTest.WebEnvironment.NONE,
        properties = [
                "spring.main.web-application-type=none",
                "logging.level.com.decurret_dcp.dcjpy.bcmonitoring=DEBUG"
        ]
)
@TestPropertySource(locations = "classpath:adhoc/application-adhoc.properties")
class ServiceStartupAdhocSpec extends Specification {

    @Shared
    static BcMonitoringAdhocHelper adhocHelper = new BcMonitoringAdhocHelper()
    
    @Shared
    static MockBesuService mockBesuService = new MockBesuService()

    @Autowired
    DownloadAbiService downloadAbiService

    @Autowired
    MonitorEventService monitorEventService

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        adhocHelper.initAdhoc(registry)
    }

    @TestConfiguration
    static class TestConfig {
        @Bean
        @Primary
        Web3j mockWeb3j() {
            return mockBesuService.getMockWeb3j()
        }
    }

    def setupSpec() {
        // Additional setup if needed
    }

    def cleanupSpec() {
        mockBesuService.stop()
        adhocHelper.stopContainer()
    }

    def "should start service successfully with all dependencies available"() {
        given: "All dependencies are available (S3, DynamoDB, WebSocket endpoint)"
        def conditions = new PollingConditions(timeout: 30, initialDelay: 1, factor: 1.25)
        
        when: "Service startup is initiated"
        // The service should start automatically via CommandLineRunner
        // We'll verify the components are properly initialized
        
        then: "ABI download service should be available and functional"
        downloadAbiService != null
        
        and: "Monitor event service should be available"
        monitorEventService != null
        
        and: "S3 bucket should be accessible and contain ABI files"
        def s3Client = adhocHelper.createS3Client()
        def response = s3Client.listObjectsV2 { builder ->
            builder.bucket("abijson-local-bucket")
        }
        response.contents().size() > 0
        s3Client.close()
        
        and: "DynamoDB tables should be accessible"
        def dynamoClient = adhocHelper.createDynamoDbClient()
        def tables = dynamoClient.listTables()
        tables.tableNames().contains("localEvents")
        tables.tableNames().contains("localBlockHeight")
        dynamoClient.close()
        
        and: "ABI download should execute successfully"
        noExceptionThrown {
            downloadAbiService.execute()
        }
    }

    def "should handle ABI download from S3 successfully"() {
        given: "S3 bucket contains valid ABI files"
        def s3Client = adhocHelper.createS3Client()
        
        when: "ABI download service is executed"
        downloadAbiService.execute()
        
        then: "No exceptions should be thrown"
        noExceptionThrown()
        
        and: "S3 objects should be accessible"
        def response = s3Client.listObjectsV2 { builder ->
            builder.bucket("abijson-local-bucket")
                   .prefix("3000/")
        }
        response.contents().size() > 0
        
        cleanup:
        s3Client.close()
    }

    def "should initialize DynamoDB connections successfully"() {
        given: "DynamoDB is available via LocalStack"
        def dynamoClient = adhocHelper.createDynamoDbClient()
        
        when: "DynamoDB operations are performed"
        def tables = dynamoClient.listTables()
        
        then: "Required tables should exist"
        tables.tableNames().contains("localEvents")
        tables.tableNames().contains("localBlockHeight")
        
        and: "Tables should be accessible for read/write operations"
        // Test block height table access
        def blockHeightTable = dynamoClient.describeTable { builder ->
            builder.tableName("localBlockHeight")
        }
        blockHeightTable.table().tableStatus().toString() == "ACTIVE"
        
        // Test events table access  
        def eventsTable = dynamoClient.describeTable { builder ->
            builder.tableName("localEvents")
        }
        eventsTable.table().tableStatus().toString() == "ACTIVE"
        
        cleanup:
        dynamoClient.close()
    }

    def "should handle WebSocket endpoint configuration"() {
        given: "Mock WebSocket endpoint is configured"
        def mockWeb3j = mockBesuService.getMockWeb3j()
        
        when: "WebSocket connection is tested"
        def blockNumber = mockWeb3j.ethBlockNumber().send()
        
        then: "Connection should be successful"
        blockNumber != null
        blockNumber.blockNumber != null
        blockNumber.blockNumber.longValue() > 0
    }

    def "should verify all required environment variables are loaded"() {
        given: "Application context is loaded"
        
        expect: "All critical configuration should be available"
        // This test verifies that the application context loads successfully
        // which means all required configuration is present
        downloadAbiService != null
        monitorEventService != null
    }
}
