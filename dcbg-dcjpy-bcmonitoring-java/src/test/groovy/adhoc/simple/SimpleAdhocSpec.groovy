package adhoc.simple

import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.application.abi.DownloadAbiService
import com.decurret_dcp.dcjpy.bcmonitoring.application.event.MonitorEventService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.test.context.TestPropertySource
import org.web3j.protocol.Web3j
import spock.lang.Specification
import static org.mockito.Mockito.mock

/**
 * Simple adhoc test to verify basic Spring Boot context loading
 * without TestContainers complexity
 */
@SpringBootTest(
        classes = [BcmonitoringApplication, SimpleAdhocSpec.TestConfig],
        webEnvironment = SpringBootTest.WebEnvironment.NONE,
        properties = [
                "spring.main.web-application-type=none",
                "logging.level.com.decurret_dcp.dcjpy.bcmonitoring=DEBUG"
        ]
)
@TestPropertySource(locations = "classpath:adhoc/application-adhoc.properties")
class SimpleAdhocSpec extends Specification {

    @Autowired
    DownloadAbiService downloadAbiService

    @Autowired
    MonitorEventService monitorEventService

    @TestConfiguration
    static class TestConfig {
        @Bean
        @Primary
        Web3j mockWeb3j() {
            return mock(Web3j.class)
        }
    }

    def "should load Spring Boot context successfully"() {
        expect: "All required beans should be available"
        downloadAbiService != null
        monitorEventService != null
    }

    def "should have proper configuration loaded"() {
        expect: "Configuration should be accessible"
        // This test verifies that the application context loads successfully
        // which means all required configuration is present
        downloadAbiService != null
        monitorEventService != null
    }

    def "should verify basic service availability"() {
        given: "Application context is loaded"
        
        expect: "Core services should be available"
        downloadAbiService != null
        monitorEventService != null
        
        and: "Services should be properly configured"
        downloadAbiService.class.simpleName == "DownloadAbiService"
        monitorEventService.class.simpleName == "MonitorEventService"
    }
}
