package adhoc.test.util

import com.fasterxml.jackson.databind.ObjectMapper
import org.testcontainers.containers.localstack.LocalStackContainer
import org.testcontainers.containers.localstack.LocalStackContainer.Service
import org.testcontainers.utility.DockerImageName
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.*
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.CreateBucketRequest
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import software.amazon.awssdk.core.sync.RequestBody

class BcMonitoringAdhocHelper {

    private static final ObjectMapper mapper = new ObjectMapper()

    private static localStackPort
    private static LocalStackContainer localStackContainer

    static String getLocalStackPort() { return localStackPort }

    static {
        startContainer()
        setupLocalStackServices()
    }
    
    private static void startContainer() {
        try {
            println "Starting LocalStack container for BC Monitoring adhoc tests..."
            localStackContainer = new LocalStackContainer(DockerImageName.parse("localstack/localstack:3.0.2"))
                    .withServices(Service.S3, Service.DYNAMODB)
                    .withStartupTimeout(java.time.Duration.ofMinutes(3))

            localStackContainer.start()
            localStackPort = String.valueOf(localStackContainer.getMappedPort(4566))
            println "LocalStack started on port: ${localStackPort}"
        } catch (Exception e) {
            println "Failed to start LocalStack container: ${e.message}"
            println "Make sure Docker is running"
            throw e
        }
    }
    
    private static void setupLocalStackServices() {
        createS3Bucket()
        createDynamoDBTables()
        uploadTestAbiFiles()
    }
    
    private static void createS3Bucket() {
        def s3Client = createS3Client()
        try {
            s3Client.createBucket(CreateBucketRequest.builder()
                    .bucket("abijson-local-bucket")
                    .build())
        } catch (Exception e) {
            // Bucket might already exist, ignore
        }
        s3Client.close()
    }
    
    private static void createDynamoDBTables() {
        def dynamoClient = createDynamoDbClient()
        
        // Create Events table
        try {
            dynamoClient.createTable(CreateTableRequest.builder()
                    .tableName("localEvents")
                    .keySchema(
                            KeySchemaElement.builder()
                                    .attributeName("transactionHash")
                                    .keyType(KeyType.HASH)
                                    .build(),
                            KeySchemaElement.builder()
                                    .attributeName("logIndex")
                                    .keyType(KeyType.RANGE)
                                    .build()
                    )
                    .attributeDefinitions(
                            AttributeDefinition.builder()
                                    .attributeName("transactionHash")
                                    .attributeType(ScalarAttributeType.S)
                                    .build(),
                            AttributeDefinition.builder()
                                    .attributeName("logIndex")
                                    .attributeType(ScalarAttributeType.N)
                                    .build()
                    )
                    .billingMode(BillingMode.PAY_PER_REQUEST)
                    .build())
        } catch (Exception e) {
            // Table might already exist, ignore
        }
        
        // Create BlockHeight table
        try {
            dynamoClient.createTable(CreateTableRequest.builder()
                    .tableName("localBlockHeight")
                    .keySchema(
                            KeySchemaElement.builder()
                                    .attributeName("id")
                                    .keyType(KeyType.HASH)
                                    .build()
                    )
                    .attributeDefinitions(
                            AttributeDefinition.builder()
                                    .attributeName("id")
                                    .attributeType(ScalarAttributeType.N)
                                    .build()
                    )
                    .billingMode(BillingMode.PAY_PER_REQUEST)
                    .build())
        } catch (Exception e) {
            // Table might already exist, ignore
        }
        
        dynamoClient.close()
    }
    
    private static void uploadTestAbiFiles() {
        def s3Client = createS3Client()
        
        // Upload sample ABI files for testing
        def sampleAbi = [
            "contractName": "TestContract",
            "abi": [
                [
                    "type": "event",
                    "name": "TestEvent",
                    "inputs": [
                        ["name": "value", "type": "uint256", "indexed": false]
                    ]
                ]
            ],
            "address": "0x1234567890123456789012345678901234567890"
        ]
        
        def abiJson = mapper.writeValueAsString(sampleAbi)
        
        s3Client.putObject(
                PutObjectRequest.builder()
                        .bucket("abijson-local-bucket")
                        .key("3000/TestContract.json")
                        .build(),
                RequestBody.fromString(abiJson)
        )
        
        s3Client.close()
    }
    
    static S3Client createS3Client() {
        return S3Client.builder()
                .endpointOverride(localStackContainer.getEndpoint())
                .region(Region.of(localStackContainer.getRegion()))
                .credentialsProvider(StaticCredentialsProvider.create(
                        AwsBasicCredentials.create(localStackContainer.getAccessKey(), localStackContainer.getSecretKey())))
                .build()
    }

    static DynamoDbClient createDynamoDbClient() {
        return DynamoDbClient.builder()
                .endpointOverride(localStackContainer.getEndpoint())
                .region(Region.of(localStackContainer.getRegion()))
                .credentialsProvider(StaticCredentialsProvider.create(
                        AwsBasicCredentials.create(localStackContainer.getAccessKey(), localStackContainer.getSecretKey())))
                .build()
    }
    
    def initAdhoc(org.springframework.test.context.DynamicPropertyRegistry registry) {
        def localStackEndpoint = localStackContainer.getEndpoint().toString()

        registry.add("aws.dynamodb.endpoint", () -> localStackEndpoint)
        registry.add("local-stack.end-point", () -> localStackEndpoint)
        registry.add("aws.access-key-id", () -> localStackContainer.getAccessKey())
        registry.add("aws.secret-access-key", () -> localStackContainer.getSecretKey())
        registry.add("aws.region", () -> localStackContainer.getRegion())
        registry.add("aws.s3.bucket-name", () -> "abijson-local-bucket")
        registry.add("aws.dynamodb.events-table-name", () -> "Events")
        registry.add("aws.dynamodb.block-height-table-name", () -> "BlockHeight")
        registry.add("aws.dynamodb.table-prefix", () -> "local")
        registry.add("env", () -> "local")
        registry.add("abi-format", () -> "hardhat")
        registry.add("websocket.uri.host", () -> "localhost")
        registry.add("websocket.uri.port", () -> "18541")
        registry.add("subscription.check-interval", () -> "1000")
        registry.add("subscription.allowable-block-timestamp-diff-sec", () -> "2")
    }
    
    def stopContainer() {
        // Keep container running for multiple tests
        // localStackContainer.stop()
    }

    static void cleanup() {
        if (localStackContainer != null) {
            localStackContainer.stop()
        }
    }
}
