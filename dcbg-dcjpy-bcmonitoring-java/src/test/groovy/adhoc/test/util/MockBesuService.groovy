package adhoc.test.util

import org.web3j.protocol.Web3j
import org.web3j.protocol.core.DefaultBlockParameter
import org.web3j.protocol.core.methods.response.*
import org.web3j.protocol.websocket.events.LogNotification
import org.web3j.protocol.websocket.events.NewHeadsNotification
import io.reactivex.Flowable
import io.reactivex.subjects.PublishSubject
import spock.lang.Specification
import java.math.BigInteger

/**
 * Mock Besu service for testing blockchain interactions without a real blockchain
 */
class MockBesuService {
    
    private Web3j mockWeb3j
    private PublishSubject<NewHeadsNotification> newHeadsSubject
    private PublishSubject<LogNotification> logSubject
    private BigInteger currentBlockNumber = BigInteger.valueOf(1000)
    
    MockBesuService() {
        this.mockWeb3j = Mock(Web3j)
        this.newHeadsSubject = PublishSubject.create()
        this.logSubject = PublishSubject.create()
        setupMockBehavior()
    }
    
    private void setupMockBehavior() {
        // Mock block number retrieval
        mockWeb3j.ethBlockNumber() >> {
            def response = Mock(EthBlockNumber)
            response.getBlockNumber() >> currentBlockNumber
            response
        }
        
        // Mock new heads subscription
        mockWeb3j.newHeadsNotifications() >> newHeadsSubject.toFlowable(io.reactivex.BackpressureStrategy.BUFFER)
        
        // Mock log subscription
        mockWeb3j.logsNotifications(_ as List) >> logSubject.toFlowable(io.reactivex.BackpressureStrategy.BUFFER)
        
        // Mock block retrieval
        mockWeb3j.ethGetBlockByNumber(_, _) >> { DefaultBlockParameter blockParam, boolean fullTx ->
            def block = createMockBlock(blockParam.getValue() as BigInteger)
            def response = Mock(EthBlock)
            response.getBlock() >> block
            response
        }
        
        // Mock filter logs
        mockWeb3j.ethGetLogs(_) >> {
            def response = Mock(EthLog)
            response.getLogs() >> []
            response
        }
    }
    
    private EthBlock.Block createMockBlock(BigInteger blockNumber) {
        def block = Mock(EthBlock.Block)
        block.getNumber() >> blockNumber
        block.getTimestamp() >> BigInteger.valueOf(System.currentTimeMillis() / 1000)
        block.getHash() >> "0x" + String.format("%064x", blockNumber.longValue())
        block.getTransactions() >> []
        return block
    }
    
    /**
     * Simulate a new block being mined
     */
    void mineBlock() {
        currentBlockNumber = currentBlockNumber.add(BigInteger.ONE)
        def block = createMockBlock(currentBlockNumber)
        def notification = Mock(NewHeadsNotification)
        notification.getParams() >> Mock(NewHeadsNotification.Params) {
            getResult() >> block
        }
        newHeadsSubject.onNext(notification)
    }
    
    /**
     * Simulate a contract event being emitted
     */
    void emitEvent(String contractAddress, String eventSignature, List<String> topics, String data) {
        def log = Mock(Log)
        log.getAddress() >> contractAddress
        log.getTopics() >> ([eventSignature] + topics)
        log.getData() >> data
        log.getTransactionHash() >> "0x" + String.format("%064x", System.currentTimeMillis())
        log.getLogIndex() >> BigInteger.ZERO
        log.getBlockNumber() >> currentBlockNumber
        
        def notification = Mock(LogNotification)
        notification.getParams() >> Mock(LogNotification.Params) {
            getResult() >> log
        }
        logSubject.onNext(notification)
    }
    
    /**
     * Simulate WebSocket handshake error
     */
    void simulateWebSocketError() {
        newHeadsSubject.onError(new RuntimeException("rpc.wsHandshakeError"))
    }
    
    /**
     * Get the mock Web3j instance
     */
    Web3j getMockWeb3j() {
        return mockWeb3j
    }
    
    /**
     * Reset the mock to initial state
     */
    void reset() {
        currentBlockNumber = BigInteger.valueOf(1000)
        newHeadsSubject = PublishSubject.create()
        logSubject = PublishSubject.create()
        setupMockBehavior()
    }
    
    /**
     * Stop the mock service
     */
    void stop() {
        newHeadsSubject.onComplete()
        logSubject.onComplete()
    }
}
