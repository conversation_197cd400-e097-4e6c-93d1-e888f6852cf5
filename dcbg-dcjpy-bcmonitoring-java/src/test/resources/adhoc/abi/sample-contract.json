{"contractName": "SampleContract", "abi": [{"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true}, {"name": "to", "type": "address", "indexed": true}, {"name": "value", "type": "uint256", "indexed": false}]}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true}, {"name": "spender", "type": "address", "indexed": true}, {"name": "value", "type": "uint256", "indexed": false}]}, {"type": "event", "name": "Mint", "inputs": [{"name": "to", "type": "address", "indexed": true}, {"name": "amount", "type": "uint256", "indexed": false}, {"name": "traceId", "type": "string", "indexed": false}]}], "address": "0x1234567890123456789012345678901234567890"}