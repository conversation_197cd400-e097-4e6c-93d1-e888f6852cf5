# Adhoc Test Configuration for BC Monitoring Service
spring.application.name=BcmonitoringAdhoc

# Server configuration
server.port=0

# AWS Configuration (will be overridden by test)
aws.region=ap-northeast-1
aws.access-key-id=access123
aws.secret-access-key=secret123
aws.dynamodb.region=ap-northeast-1
aws.dynamodb.table-prefix=local
aws.dynamodb.endpoint=http://localhost:4566
aws.s3.bucket-name=abijson-local-bucket
aws.s3.region=ap-northeast-1
aws.dynamodb.events-table-name=Events
aws.dynamodb.block-height-table-name=BlockHeight

# Ethereum configuration (mocked)
ethereum.endpoint=

# WebSocket configuration (mocked)
websocket.uri.host=localhost
websocket.uri.port=18541

# Subscription configuration
subscription.check-interval=1000
subscription.allowable-block-timestamp-diff-sec=2

# Environment configuration
env=local
abi-format=hardhat

# LocalStack configuration
local-stack.end-point=http://localhost:4566
local-stack.region=ap-northeast-1
local-stack.access-key=access123
local-stack.secret-key=secret123

# Disable auto-startup for tests
spring.main.web-application-type=none

# Logging configuration for tests
logging.level.com.decurret_dcp.dcjpy.bcmonitoring=DEBUG
logging.level.software.amazon.awssdk=WARN
logging.level.org.testcontainers=INFO
